// Polyfill for BackHandler to support old libraries (must be imported first)
import '@/polyfills/backhandler.polyfill'

import { UpdateVersionPopup } from '@/components/Popup/UpdateVersionPopup/UpdateVersionPopup'
import { initI18n } from '@/configs/i18n/index'
import { Providers } from '@/providers'
import '@/styles/global.css'
import { setAudioModeAsync } from 'expo-audio'

import { useScreenTracking } from '@/analytics/hooks/useScreenTracking'
import { useFonts } from 'expo-font'
import * as Notifications from 'expo-notifications'
import { Stack } from 'expo-router'
import * as SplashScreen from 'expo-splash-screen'
import { useEffect, useState } from 'react'
import { View } from 'react-native'
import { GestureHandlerRootView } from 'react-native-gesture-handler'
import { configureReanimatedLogger, ReanimatedLogLevel } from 'react-native-reanimated'
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false,
})

// Import providers
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync()

SplashScreen.setOptions({
  duration: 400,
  fade: true,
})

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
})

// Audio Config

// await setAudioModeAsync({
//   playsInSilentMode: true,
//   shouldPlayInBackground: true,
//   interruptionModeAndroid: 'duckOthers',
//   interruptionMode: 'mixWithOthers',
// })

let GoogleSignin: any = null
if (process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production') {
  //eslint-disable-next-line @typescript-eslint/no-require-imports
  GoogleSignin = require('@react-native-google-signin/google-signin').GoogleSignin
  GoogleSignin.configure({
    iosClientId: process.env.EXPO_PUBLIC_OAUTH_IOS_CLIENT_ID!,
    webClientId: process.env.EXPO_PUBLIC_WEB_CLIENT_ID!,
    offlineAccess: true,
    forceCodeForRefreshToken: true,
    scopes: ['profile', 'email'],
  })
}

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  })
  const [i18nInitialized, setI18nInitialized] = useState(false)

  useEffect(() => {
    const initializeI18n = async () => {
      try {
        await initI18n()
        setI18nInitialized(true)
      } catch (error) {
        console.error('Failed to initialize i18n:', error)
        // Set to true anyway to prevent infinite loading
        setI18nInitialized(true)
      }
    }

    initializeI18n()
  }, [])

  useEffect(() => {
    if (loaded && i18nInitialized) {
      setTimeout(() => {
        SplashScreen.hideAsync()
      }, 0)
    }
  }, [loaded, i18nInitialized])

  useEffect(() => {
    async function setupAudio() {

      try {
        await setAudioModeAsync({
          playsInSilentMode: true,
          shouldPlayInBackground: true,
          interruptionModeAndroid: 'duckOthers',
          interruptionMode: 'mixWithOthers',
          allowsRecording: true,
        })
        console.log('Audio mode configured ✅')
      } catch (err) {
        console.error('Failed to set audio mode:', err)
      }
    }


    setupAudio()
  }, [])
  // Screen Tracking
  useScreenTracking()

  if (!loaded || !i18nInitialized) {
    return null
  }

  return (
    <View style={{ flex: 1, backgroundColor: 'white' }}>
      {/* <StatusBar style="light" /> */}
      <GestureHandlerRootView style={{ flex: 1, backgroundColor: 'white' }}>
        <Providers>
          <>
            <UpdateVersionPopup />

            <Stack>
              <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
              <Stack.Screen name="(main)" options={{ headerShown: false }} />
              <Stack.Screen name="(auth)" options={{ headerShown: false }} />
              <Stack.Screen name="+not-found" />
            </Stack>
          </>
        </Providers>
      </GestureHandlerRootView>
    </View>
  )
}
