import { useEffect } from "react"
import { View } from "react-native"
import Animated, { Easing, useAnimatedStyle, useSharedValue, withRepeat, withTiming } from "react-native-reanimated"
import { useMedicalFacultiesStore } from "../../stores/MedicalFacultiesStores"

import StartLoadingIcon from '@/assets/icons/starLoading.svg'

export const LoadingHico = () => {

    const { isLoading, showLoading, hiddenLoading } = useMedicalFacultiesStore()

    const spin = useSharedValue(0);

    const animatedStyle = useAnimatedStyle(() => {
        const spinValue = spin.value + 'deg';

        return {
            transform: [{ rotate: spinValue }],
        };
    });

    useEffect(() => {
        spin.value = withRepeat(
            withTiming(360, {
                duration: 2000,
                easing: Easing.linear,
            }),
            -1,
            false
        );
    }, []);

    useEffect(() => {
        showLoading()

        return (() => {
            hiddenLoading()
        })
    }, [])

    return isLoading && (
        <View className="h-full flex flex-col-reverse justify-end w-full absolute inset-0">
            <View className='w-full h-[350px] z-50 bg-custom-danger-600 flex items-center justify-center'>
                <Animated.View style={[animatedStyle]}>
                    <StartLoadingIcon />
                </Animated.View>
            </View>

            <View style={{ backgroundColor: 'rgba(255, 255, 255, 0.4)' }} className='flex-1'></View>
        </View>
    )
}
