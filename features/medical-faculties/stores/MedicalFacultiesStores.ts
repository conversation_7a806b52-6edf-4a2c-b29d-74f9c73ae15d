import { create } from "zustand"
import { devtools } from "zustand/middleware"


export interface MedicalFacultiesState {
  isLoading: boolean

}


export interface MedicalFacultiesActions {
    hiddenLoading: () => void
    showLoading: () => void
} 

const initialState: MedicalFacultiesState = {
  isLoading: false,
}

export const useMedicalFacultiesStore = create<MedicalFacultiesState & MedicalFacultiesActions>()(
  devtools(
    (set) => ({
      ...initialState,
      hiddenLoading: () => {
        set({ isLoading: false })
      },
      showLoading: () => {
        set({ isLoading: true })
      },
    }),
  ),
)
